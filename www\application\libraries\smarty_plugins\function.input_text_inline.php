<?php

require_once("function.input_generic.php");

function smarty_function_input_text_inline($params, &$smarty) {
	extract($params);
	
	$prop = isset_or($prop, "text");
	$params["prop"] = $prop;
	$params["label"] = isset_or($label, "Текст");
	$params["css"] = isset_or($css, "tinymce-inline");
	
	$object = arr($params, "object", $smarty->getVariable("object")->value);
	$lang = $smarty->getVariable("lang")->value;
	
	$name_prefix = arr($params, "prefix", "object");
	$params["name"] = sprintf("%s[locale_data][%s][%s]", $name_prefix, $lang->id, $prop);
	
	if (!array_key_exists("value", $params)) {
		$prop = $params["prop"];
		$params["value"] = $object->locale($lang->id)->$prop;
	}
	
	return smarty_function_input_generic($params, $smarty);
}
