<?php

class ci_bean extends bean {

	// used in memory caching for faster referencing
	public static function indexes() {
		return array();
	}

	// db table name for bean. Default is (class_name + "s").
	public static function table() {
		return get_called_class() . "s";
	}

	// file upload key
	public static function files_key() {
		return "";
	}

	public function save_file($file) {
		return get_instance()->filemanager->upload($file, $this->files_key());

	}
	public function delete_file($filename) {
		return get_instance()->filemanager->delete($filename, $this->files_key());
	}

	public function get_parent_object($table, $id) {
		return get_instance()->data->get_loaded($table, $id);
	}
	public function get_child_objects($table, $foreign_key, $value) {
		return get_instance()->data->get_loaded($table, $value, $foreign_key);
	}
}