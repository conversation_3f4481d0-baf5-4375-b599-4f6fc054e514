<?php
class Cart {
	const SESSION_KEY = "_cart";
	const MAX_PRODUCT_COUNT = 100;
	
	private $data;

	
	public function __construct() {
		$data = _session(self::SESSION_KEY);

		if ($data) {
			$this->data = $data;
		} else {
			$this->init();
		}
	}
	
	public function init() {
		$this->data = (object)array(
				"products" => array(),
		);
		set_session(self::SESSION_KEY, $this->data);
	}
	
	public function add_product($id, $qty) {
		$info = $this->get_products(array($id));

		if (!$info) {
			return false;
		}
		$keys = array_keys($info);

		if (isset($this->data->products[$id])) {
			$this->change_qty_by($id, $qty);
		} else {
			$this->data->products[$id] = (object)$info[$id];
			$this->change_qty($id, $qty);
		}

		return true;
	}

	private function get_products($ids) {
		
		$filter = array(
			"kinds" => node::KIND_PROD,
			"ids" => $ids,
		);
		$products = get_instance()->data->nodes->get_list_loc(1, $filter);
		
		
		$res = array();
		foreach ($ids as $id) {
			$product = __::find($products, function($o) use ($id) {
					return $o->id == $id;
				});
			if (!$product) {
				continue;
			}

			$res[$product->id] = (object)array(
					"id" => $product->id, 
					"title" => $product->title,
					"image" => $product->image,
					"path" => $product->full_url,
					"refno" => $product->refno,
					"subtitle" => $product->subtitle,
			);
		}

		return $res;
	}
	
	public function sync() {
		$ids = array();
		foreach ($this->data->products as $i) {
			$ids[] = $i->id;
		}
		$data = $this->get_products($ids);

		foreach ($this->data->products as $id=>$i) {
			if (!isset($data[$id])) {
				$this->delete($i->id);
				continue;
			}
			$this->data->products[$id] = $data[$id];
			$this->change_qty($i->id, $i->qty);
		}

	}
	
	public function delete($id) {
		unset($this->data->products[$id]);
	}
	
	public function change_qty($id, $qty) {
		if (isset($this->data->products[$id])) {
			$product = $this->data->products[$id];

			$qty = min($qty, self::MAX_PRODUCT_COUNT);
			$qty = max(0, $qty);

			if ($qty > 0) {
				$product->qty = $qty;
			} else {
				$this->delete($id);
			}
		}
	}
	private function change_qty_by($id, $change_by) {
		if (isset($this->data->products[$id])) {
			$qty = $this->data->products[$id]->qty + $change_by;
			$this->change_qty($id, $qty);
		}
	}
	
	public function count() {
		$res = 0;
		foreach($this->data->products as $row) {
			$res += $row->qty;
		}
		return $res;
	}
	
	public function products() {
		return $this->data->products;
	}
}