<?php

class Validator {
	const DEFAULT_MESSAGE = "Invalid value (%s)";
	const REQUIRED = "/.+/";
	const EMAIL = "/^([a-z0-9\+_\-]+)(\.[a-z0-9\+_\-]+)*@([a-z0-9\-]+\.)+[a-z]{2,6}$/ix";
	const PHONE = "/^[0-9 +-\/]{6,}$/";
	const NAME = "/^[a-z\p{Cyrillic} ]{3,40}$/ui";
	const PASSWORD = "/^.{6,}$/";
	const URL = "/^(https?):\/\/(.)+/";
	const IMAGE_EXTS = "jpg, jpeg, png, gif, svg";
	const CV_EXTS = "jpg, jpeg, doc, docx, pdf";
	const VIDEO_EXTS = "mp4, ogg, ogv, webm";
	const AUDIO_EXTS = "mp3, ogg";
	const XLS_EXTS = "xlsx";
	
	private $invalid = array();
	
	public function set($name, $valid=false) {
		if ($valid) {
			$index = array_search($name, $this->invalid);
			if ($index !== false) {
				unset($this->invalid[$index]);
				$this->invalid = array_values($this->invalid);
			}
		} else if ($this->get($name)) {
			$this->invalid[] = $name;
		}
	}
	
	public function get($name) {
		return !in_array($name, $this->invalid);
	}
	
	public function is_valid() {
		return count($this->invalid) === 0;
	}
	
	public function get_invalid() {
		return $this->invalid;
	}
	
	
	public function errors($messages=array()) {
		return self::map_errors($this->get_invalid(), $messages);
	}
	
	public static function map_errors($invalid, $messages) {
		$res = array();
		
		foreach ($invalid as $i) {
			if (isset($messages[$i])) {
				$res[] = $messages[$i];
			} else {
				$res[] = sprintf(self::DEFAULT_MESSAGE, $i);
			}
		}
		return $res;
	}
	
	public function as_string($messages, $glue="\n") {
		$errors = $this->errors($messages);
		return implode($glue, $errors);
	}
	
	public function merge($other, $prefix="", $suffix="") {
		foreach ($other->get_invalid() as $i) {
			$this->set($prefix . $i . $suffix);
		}
	}
	
	public function string($input, $name, $pattern) {
		$res = preg_match($pattern, $input);
		$this->set($name, $res);
		return $res;
	}
	
	public function required($input, $name) {
		return $this->string($input, $name, self::REQUIRED);
	}
	
	public function email($input, $name) {
		return $this->string($input, $name, self::EMAIL);
	}
	
	public function password($input, $name) {
		return $this->string($input, $name, self::PASSWORD);
	}
	
	public function name($input, $name) {
		return $this->string($input, $name, self::NAME);
	}
	
	public function phone($input, $name) {
		return $this->string($input, $name, self::PHONE);
	}
	
	public function jstest($input, $name) {
		$res = jstest($input);
		$this->set($name, $res);
		return $res;
	}
	
	public function int($input, $name, $min=null, $max=null) {
		$res = self::test_int($input, $min, $max);
		$this->set($name, $res);
		return $res;
	}
	
	public function float($input, $name, $min=null, $max=null) {
		$res = self::test_float($input, $min, $max);
		$this->set($name, $res);
		return $res;
	}
	
	public function arr($input, $name, $allowed) {
		$res = in_array($input, $allowed);
		$this->set($name, $res);
		return $res;
	}
	
	public static function test_required($input) {
		return preg_match(self::REQUIRED, $input);
	}
	
	public static function test_int($input, $min=null, $max=null) {
		$input = intval($input);
		return 
			($min === null || $input >= $min) &&
			($max === null || $input <= $max);
	}
	
	public static function test_float($input, $min=null, $max=null) {
		$input = floatval($input);
		
		return 
			($min === null || $input >= $min) &&
			($max === null || $input <= $max);
	}
	
	public static function test_file_upload($upload) {
		return isset($upload["tmp_name"]) && is_uploaded_file($upload["tmp_name"]) && isset($upload["name"]);
	}
	
	public static function test_file_ext($file, $exts) {
		$ext = mb_strtolower(pathinfo($file, PATHINFO_EXTENSION));
		$allowed = explode(",", $exts);
		$allowed = array_map("trim", $allowed);
		$allowed = array_map("mb_strtolower", $allowed);
		return in_array($ext, $allowed);
	}
	
	public static function test_image_upload($upload) {
		return self::test_file_upload($upload) && self::test_image_ext($upload["name"]);
	}
	public static function test_cv_upload($upload) {
		return self::test_file_upload($upload) && self::test_cv_ext($upload["name"]);
	}
	public static function test_video($upload) {
		return self::test_file_upload($upload) && self::test_video_ext($upload["name"]);
	}
	public static function test_audio($audio) {
		return self::test_file_upload($upload) && self::test_audio_ext($upload["name"]);
	}
	
	public static function test_image_ext($file) {
		return self::test_file_ext($file, self::IMAGE_EXTS);
	}
	public static function test_cv_ext($file) {
		return self::test_file_ext($file, self::CV_EXTS);
	}
	public static function test_video_ext($file) {
		return self::test_file_ext($file, self::VIDEO_EXTS);
	}
	public static function test_audio_ext($file) {
		return self::test_file_ext($file, self::AUDIO_EXTS);
	}
}