<?php

class Image {
	const CROP = 0;
	const SCALE = 1;
	const FIT = 2;

	const DEFAULT_JPEG_QUALITY = 85;
	const DEFAULT_WATERMARK_POSITION = 5;
	const DEFAULT_FORMAT = "jpg";

	private $resource = null;
	private $width = 0;
	private $height = 0;
	private $file = null;
	private $format = null;

	public static function from_file($file, $format=null) {
		return new self($file, $format);
	}
	public static function from_resource($resource, $format) {
		return new self(null, $format, $resource);
	}
	public static function validate($file, $format=null) {
		$img = new self($file, $format);
		return $img->is_valid();
	}
	public static function create_empty($width, $height, $format) {
		$res = imagecreatetruecolor($width, $height);
		imagealphablending($res, true);
		imagesavealpha($res, true);
		return new self(null, $format, $res);
	}

	private function __construct($file=null, $format=null, $resource=null) {
		if ($file) {
			$this->file = $file;
			$this->format = $format ? $format : self::get_format($file);
			$resource = $this->open($this->file, $this->format);
		}
		$this->resource = $resource;
		if ($this->is_valid()) {
			$this->width = imagesx($this->resource);
			$this->height = imagesy($this->resource);
		}
		if (!$this->format) {
			$this->format = $format ? $format : self::DEFAULT_FORMAT;
		}
	}
	public function __destruct() {
		if ($this->is_valid()) {
			imagedestroy($this->resource);
		}
	}
	public function copy() {
		$res = self::create_empty($this->width, $this->height, $this->format);
		imagecopy($res->resource, $this->resource, 0, 0, 0, 0, $this->width, $this->height);
		return $res;
	}

	private function open($file) {
		$res = null;
		switch($this->format) {
			case "gif" :
				$res = @imagecreatefromgif($file);
				break;
			case "png" :
				$res = @imagecreatefrompng($file);
				break;
			case "jpg" :
			case "jpeg" :
				$res = @imagecreatefromjpeg($file);
				break;
			default: throw new exception("Unsupported image format: $this->format");
		}
		return $res ? $res : null;
	}
	public static function get_format($file) {
		$info = pathinfo($file);
		return isset($info["extension"]) ? strtolower($info["extension"]) : "";
	}

	public function resize($size, $method=self::CROP, $fill=null, $crop_data=null) {
		$image_x = imagesx($this->resource);
		$image_y = imagesy($this->resource);

		$thumb_width = 0;
		$thumb_height = 0;
		$src_x = 0;
		$src_y = 0;
		$dst_x = 0;
		$dst_y = 0;
		$dst_w = 0;
		$dst_h = 0;
		$src_w = 0;
		$src_h = 0;
		switch ($method) {
			case self::CROP:
				if ($image_x * $size[1] < $image_y * $size[0]) {
					$crop_width = $image_x;
					$crop_height = round($image_x * $size[1] / $size[0]);
				} else {
					$crop_width = round($image_y * $size[0] / $size[1]);
					$crop_height = $image_y;
				}
				$thumb_width = $size[0];
				$thumb_height = $size[1];
				$src_x = $crop_data ? $crop_data["x"] : round(($image_x - $crop_width) / 2);
				$src_y = $crop_data ? $crop_data["y"] : round(($image_y - $crop_height) /2);
				$dst_w = $size[0];
				$dst_h = $size[1];
				$src_w = $crop_data ? $crop_data["w"] : $crop_width;
				$src_h = $crop_data ? $crop_data["h"] : $crop_height;
			break;
			case self::SCALE:
				if($image_x * $size[1] < $image_y * $size[0]) {
					$thumb_width = round($size[1] * $image_x / $image_y);
					$thumb_height = $size[1];
				} else {
					$thumb_width = $size[0];
					$thumb_height = round($size[0] * $image_y  / $image_x);
				}
				$src_x = 0;
				$src_y = 0;
				$dst_w = $thumb_width;
				$dst_h = $thumb_height;
				$src_w = $image_x;
				$src_h = $image_y;
			break;
			case self::FIT:
				$thumb_width = $size[0];
				$thumb_height = $size[1];
				if($image_x * $size[1] < $image_y * $size[0]) {
					$w = round($size[1] * $image_x / $image_y);
					$h = $size[1];
				} else {
					$w = $size[0];
					$h = round($size[0] * $image_y  / $image_x);
				}
				$dst_x = round(($size[0] - $w) / 2);
				$dst_y = round(($size[1] - $h) / 2);
				$src_x = 0;
				$src_y = 0;
				$dst_w = $w;
				$dst_h = $h;
				$src_w = $image_x;
				$src_h = $image_y;
				if ($fill) {
					$fill = $this->get_color($fill[0], $fill[1], $fill[2]);
				}
			break;
			default:
				throw new exception("Unknown resize method: $method");
			break;

		}
		$res = imagecreatetruecolor($thumb_width, $thumb_height);
		if ($fill) {
			imagefill($res, 0, 0, $fill);
		}
		imagealphablending($res, false);
		imagesavealpha($res, true);
		imagecopyresampled($res, $this->resource, $dst_x, $dst_y, $src_x, $src_y, $dst_w, $dst_h, $src_w, $src_h);
		return self::from_resource($res, $this->format);
	}
	public function rotate($angle, $background=0) {
		return self::from_resource(imagerotate($this->resource, $angle, $background), $this->format);
	}
	public function watermark($watermark, $position=null) {
		if (!$position) {
			$position = self::DEFAULT_WATERMARK_POSITION;
		}
		$image_x = $this->width;
		$image_y = $this->height;

		$watermark_x = $watermark->width;
		$watermark_y = $watermark->height;

		if (is_int($position)) {
			$position = position2d::from_index($position);
		}
		$point = $position->get_inner_point(array($image_x, $image_y), array($watermark_x, $watermark_y));

		$res = $this->copy();

		imagecopy($res->resource, $watermark->resource, $point[0], $point[1], 0, 0, $watermark_x, $watermark_y);
		return $res;
	}

	public function get_color($r, $g, $b) {
		return imagecolorallocate($this->resource, $r, $g, $b);
	}
	public function save($filename, $quality=self::DEFAULT_JPEG_QUALITY) {
		$format = $this->get_format($filename);
		switch($format) {
			case "gif":
				imagegif($this->resource, $filename);
				break;
			case "png":
				imagealphablending($this->resource, false);
				imagesavealpha($this->resource, true);
				imagepng($this->resource, $filename);
				break;
			case "jpg":
			case "jpeg":
				imagejpeg($this->resource, $filename, $quality);
				break;
			default: throw new exception("Unsupported image format: $this->format");
		}
	}
	public function get_binary($quality=self::DEFAULT_JPEG_QUALITY) {
		ob_start();

		switch($this->format) {
			case "gif":
				imagegif($this->resource);
				break;
			case "png":
				imagealphablending($this->resource, false);
				imagesavealpha($this->resource, true);
				imagepng($this->resource);
				break;
			case "jpg":
			case "jpeg":
				imagejpeg($this->resource, null, $quality);
				break;
			default: throw new exception("Unsupported image format: $this->format");
		}
		$res = ob_get_clean();
		return $res;
	}
	public function is_valid() {
		return $this->resource !== null;
	}

	public static function is_animated($file) {
		$info = pathinfo($file);

		$fh = null;

		if ($info["extension"] !== "gif" || !($fh = @fopen($file, 'rb'))) {
			return false;
		}

		$frames = 0;

		while(!feof($fh) && $frames < 2) {
			$chunk = fread($fh, 1024 * 100);
			$frames += preg_match_all('#\x00\x21\xF9\x04.{4}\x00[\x2C\x21]#s', $chunk, $matches);
		}

		fclose($fh);
		return $frames > 1;
	}

	public static function is_svg($file) {
		$info = pathinfo($file);

		return mb_strtolower($info["extension"]) == "svg";
	}

	public function __get($name) {
		return $this->$name;
	}
}
