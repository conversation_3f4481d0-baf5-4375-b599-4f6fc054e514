<?php

function smarty_function_webresources($params, &$smarty) {
	extract($params);

	$CI =& get_instance();

	$icon = isset_or($icon, "");
	$skin = isset_or($skin, "");
	$tab = isset_or($tab, "\t");

	if ($icon) {
		$icon = conf("webresources_icons", $icon);

		if ($skin) {
			$icon = str_replace("[skin]", $skin, $icon);
		}

		return sprintf('<link href="%s%s" rel="shortcut icon" />', $CI->path->base, $icon);
	} else {
		$webresources = conf("webresources", $bundle);
		if (!count($webresources)) {
			return;
		}

		$segments = explode(".", $webresources[0]);
		$ext = strtolower(array_pop($segments));
		$template = "";
		switch ($ext) {
			case "js":
				$template = '<script src="%s"></script>';
			break;
			case "css":
			case "less":
				$template = '<link href="%s" type="text/css" rel="stylesheet" />';
			break;
			default: throw new exception("Unknown file extension: \"{$ext}\".");
		}
		
		$ver = conf("webresources_ver");
		$minify = conf("webresources_minify");

		$res = array();

		$next = "?";
		if ($ver) {
			$ver = "{$next}v={$ver}";
			$next = "&";
		}
		
		if ($minify) {
			if ($skin) {
				$skin = "{$next}skin={$skin}";
				$next = "&";
			}
			$item = sprintf("%sresource/min/g=%s%s%s", $CI->path->base, $bundle, $skin, $ver);
			$res[] = sprintf($template, $item);
		} else {
			foreach ($webresources as $item) {
				if ($skin) {
					$item = str_replace("[skin]", $skin, $item);
				}
				$item = $CI->path->base . $item . $ver;
				$res[] = sprintf($template, $item);
			}
		}

		return implode("\n{$tab}", $res);
	}
}
