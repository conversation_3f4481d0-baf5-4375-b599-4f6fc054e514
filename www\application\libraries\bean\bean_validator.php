<?php

abstract class bean_validator extends Validator {
	private $object;

	public function __construct(bean $object) {
		$this->object = $object;
	}
	
	public function object() {
		return $this->object;
	}
	
	public function get_messages() {
		return static::$DEF_MESSAGES;
	}
	
	public function errors($messages=array()) {
		$messages = ($messages ? $messages : $this->get_messages());
		return parent::errors($messages);
	}
	
	public function validate($set="def") {
		$set = mb_strtoupper($set);
		$class = get_class($this);
		if (!isset($class::$$set)) {
			throw new exception(sprintf("bean of type %s has no %s validation set", get_class($this), $set));
		}
		
		foreach ($class::$$set as $name=>$validator) {
			$is_custom = ($validator[0] == "_");
			if ($is_custom) {
				$res = call_user_func_array(array($this, $validator), array($this->object));
				$this->set($name, $res);
			} else {
				$validator = (array)$validator;
				
				$method = $validator[0];
				if (!method_exists($this, $method)) {
					throw new exception(sprintf("method %s does not exists for validator %s", $method, get_class($this)));
				}
				
				$value = $this->object->$name;
				$params = array($value, $name);
				if (isset($validator[1])) {
					unset($validator[0]);
					$params = array_merge($params, $validator);
				}
				call_user_func_array(array($this, $method), $params);
			}
		}
		
		return $this->is_valid();
	}
	
	public function file($input, $name) {
		$file = $this->object->get_file($name);
		$res = self::test_file_upload($file) || $name;
		$this->set($name, $res);
		return $res;
	}
	public function image($input, $name) {
		$file = $this->object->get_file($name);
		$res = self::test_image_upload($file) || !self::test_file_upload($file) && $input;
		$this->set($name, $res);
		return $res;
	}
	public function image_opt($input, $name) {
		$file = $this->object->get_file($name);
		if (self::test_file_upload($file)) {
			$res = self::test_image_upload($file) || $input;
			$this->set($name, $res);
			return $res;
		}
		return true;
	}
}