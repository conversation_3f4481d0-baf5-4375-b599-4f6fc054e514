@charset "UTF-8";
/*! destyle.css v1.0.11 | MIT License | https://github.com/nicolas-cusan/destyle.css */
/* Reset box-model
   ========================================================================== */
* {
  box-sizing: border-box; }

::before,
::after {
  box-sizing: inherit; }

/* Document
   ========================================================================== */
/**
 * 1. Correct the line height in all browsers.
 * 2. Prevent adjustments of font size after orientation changes in iOS.
 * 3. Remove gray overlay on links for iOS.
 */
html {
  line-height: 1.15;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
  -webkit-tap-highlight-color: transparent;
  /* 3*/ }

/* Sections
   ========================================================================== */
/**
 * Remove the margin in all browsers.
 */
body {
  margin: 0; }

/**
 * Render the `main` element consistently in IE.
 */
main {
  display: block; }

/* Vertical rhythm
   ========================================================================== */
p,
table,
blockquote,
address,
pre,
iframe,
form,
figure,
dl {
  margin: 0; }

/* Headings
   ========================================================================== */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  line-height: inherit;
  font-weight: inherit;
  margin: 0; }

/* Lists (enumeration)
   ========================================================================== */
ul,
ol {
  margin: 0;
  padding: 0;
  list-style: none; }

/* Lists (definition)
   ========================================================================== */
dt {
  font-weight: bold; }

dd {
  margin-left: 0; }

/* Grouping content
   ========================================================================== */
/**
 * 1. Add the correct box sizing in Firefox.
 * 2. Show the overflow in Edge and IE.
 */
hr {
  box-sizing: content-box;
  /* 1 */
  height: 0;
  /* 1 */
  overflow: visible;
  /* 2 */
  border: 0;
  border-top: 1px solid;
  margin: 0;
  clear: both;
  color: inherit; }

/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */
pre {
  font-family: monospace, monospace;
  /* 1 */
  font-size: inherit;
  /* 2 */ }

address {
  font-style: inherit; }

/* Text-level semantics
   ========================================================================== */
/**
 * Remove the gray background on active links in IE 10.
 */
a {
  background-color: transparent;
  text-decoration: none;
  color: inherit; }

/**
 * 1. Remove the bottom border in Chrome 57-
 * 2. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari.
 */
abbr[title] {
  border-bottom: none;
  /* 1 */
  text-decoration: underline;
  /* 2 */
  text-decoration: underline dotted;
  /* 2 */ }

/**
 * Add the correct font weight in Chrome, Edge, and Safari.
 */
b,
strong {
  font-weight: bolder; }

/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */
code,
kbd,
samp {
  font-family: monospace, monospace;
  /* 1 */
  font-size: inherit;
  /* 2 */ }

/**
 * Add the correct font size in all browsers.
 */
small {
  font-size: 80%; }

/**
 * Prevent `sub` and `sup` elements from affecting the line height in
 * all browsers.
 */
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline; }

sub {
  bottom: -0.25em; }

sup {
  top: -0.5em; }

/* Embedded content
   ========================================================================== */
/**
 * Remove the border on images inside links in IE 10.
 */
img {
  border-style: none;
  vertical-align: bottom; }

embed,
object,
iframe {
  border: 0;
  vertical-align: bottom; }

/* Forms
   ========================================================================== */
/**
 * Reset form fields to make them styleable
 * 1. Reset radio and checkbox to preserve their look in iOS.
 */
button,
input,
optgroup,
select,
textarea {
  -webkit-appearance: none;
  appearance: none;
  vertical-align: middle;
  color: inherit;
  font: inherit;
  border: 0;
  background: transparent;
  padding: 0;
  margin: 0;
  outline: 0;
  border-radius: 0;
  text-align: inherit; }

[type='checkbox'] {
  /* 1 */
  -webkit-appearance: checkbox;
  appearance: checkbox; }

[type='radio'] {
  /* 1 */
  -webkit-appearance: radio;
  appearance: radio; }

/**
 * Show the overflow in IE.
 * 1. Show the overflow in Edge.
 */
button,
input {
  /* 1 */
  overflow: visible; }

/**
 * Remove the inheritance of text transform in Edge, Firefox, and IE.
 * 1. Remove the inheritance of text transform in Firefox.
 */
button,
select {
  /* 1 */
  text-transform: none; }

/**
 * Correct the inability to style clickable types in iOS and Safari.
 */
button,
[type='button'],
[type='reset'],
[type='submit'] {
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none; }

button[disabled],
[type='button'][disabled],
[type='reset'][disabled],
[type='submit'][disabled] {
  cursor: default; }

/**
 * Remove the inner border and padding in Firefox.
 */
button::-moz-focus-inner,
[type='button']::-moz-focus-inner,
[type='reset']::-moz-focus-inner,
[type='submit']::-moz-focus-inner {
  border-style: none;
  padding: 0; }

/**
 * Restore the focus styles unset by the previous rule.
 */
button:-moz-focusring,
[type='button']:-moz-focusring,
[type='reset']:-moz-focusring,
[type='submit']:-moz-focusring {
  outline: 1px dotted ButtonText; }

/**
 * Reset to invisible
 */
fieldset {
  margin: 0;
  padding: 0;
  border: 0;
  min-width: 0; }

/**
 * 1. Correct the text wrapping in Edge and IE.
 * 2. Correct the color inheritance from `fieldset` elements in IE.
 * 3. Remove the padding so developers are not caught out when they zero out
 *    `fieldset` elements in all browsers.
 */
legend {
  color: inherit;
  /* 2 */
  display: table;
  /* 1 */
  max-width: 100%;
  /* 1 */
  padding: 0;
  /* 3 */
  white-space: normal;
  /* 1 */ }

/**
 * Add the correct vertical alignment in Chrome, Firefox, and Opera.
 */
progress {
  vertical-align: baseline; }

/**
 * Remove the default vertical scrollbar in IE 10+.
 */
textarea {
  overflow: auto; }

/**
 * 1. Remove the padding in IE 10.
 */
[type='checkbox'],
[type='radio'] {
  padding: 0;
  /* 1 */ }

/**
 * Correct the cursor style of increment and decrement buttons in Chrome.
 */
[type='number']::-webkit-inner-spin-button,
[type='number']::-webkit-outer-spin-button {
  height: auto; }

/**
 * 1. Correct the outline style in Safari.
 */
[type='search'] {
  outline-offset: -2px;
  /* 1 */ }

/**
 * Remove the inner padding in Chrome and Safari on macOS.
 */
[type='search']::-webkit-search-decoration {
  -webkit-appearance: none; }

/**
 * 1. Correct the inability to style clickable types in iOS and Safari.
 * 2. Change font properties to `inherit` in Safari.
 */
::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */ }

/**
 * Clickable labels
 */
label[for] {
  cursor: pointer; }

/* Interactive
   ========================================================================== */
/*
 * Add the correct display in Edge, IE 10+, and Firefox.
 */
details {
  display: block; }

/*
 * Add the correct display in all browsers.
 */
summary {
  display: list-item; }

/* Table
   ========================================================================== */
table {
  border-collapse: collapse;
  border-spacing: 0; }

caption {
  text-align: left; }

td,
th {
  vertical-align: top; }

th {
  text-align: left;
  font-weight: bold; }

/* Misc
   ========================================================================== */
/**
 * Add the correct display in IE 10+.
 */
template {
  display: none; }

/**
 * Add the correct display in IE 10.
 */
[hidden] {
  display: none; }

body {
  font-family: 'Poppins', sans-serif;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.5; }

header {
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.07); }
  header.sticky .bottom {
    top: 0;
    z-index: 10;
    position: fixed;
    background: #fff;
    width: 100%;
    box-shadow: 0px 1px 10px rgba(0, 0, 0, 0.1); }
  header .top {
    background: #063B88; }
    header .top .wrap {
      max-width: 1400px;
      margin: 0 auto;
      display: flex; }
    header .top ul {
      display: flex; }
    header .top .logos {
      background: #fff; }
      header .top .logos li {
        margin: 0 10px;
        padding: 10px; }
      header .top .logos img {
        height: 40px; }
        @media (min-width: 1280px) {
          header .top .logos img {
            height: 60px; } }
    header .top form {
      margin: 0 auto;
      align-items: center;
      flex: 1;
      padding: 0 30px;
      display: none; }
      @media (min-width: 768px) {
        header .top form {
          display: flex; } }
      header .top form input {
        padding: 8px 16px;
        font-size: 16px;
        line-height: 16px;
        border-radius: 5px;
        color: #495057;
        background: #fff;
        border: 1px solid #ced4da;
        width: 100%; }
        header .top form input::placeholder {
          color: #6c757d;
          opacity: 1; }
        header .top form input:focus {
          border-color: #80bdff;
          outline: 0;
          box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25); }
      header .top form button {
        color: #fff;
        margin-left: 5px; }
    header .top .contacts {
      display: none; }
      @media (min-width: 768px) {
        header .top .contacts {
          display: flex; } }
      header .top .contacts li {
        border-left: 1px solid rgba(250, 250, 250, 0.3);
        padding: 0px 10px;
        display: flex;
        align-items: center; }
        header .top .contacts li:last-child {
          border-right: 1px solid rgba(250, 250, 250, 0.3); }
        header .top .contacts li i {
          margin-right: 5px;
          font-size: 18px; }
        header .top .contacts li a {
          color: #fff;
          font-size: 12px;
          font-weight: 600; }
          @media (min-width: 1280px) {
            header .top .contacts li a {
              font-size: 14px; } }
    header .top a.menu {
      display: block;
      width: 3z0px;
      height: 30px;
      color: #fff;
      margin: 10px 25px 0 auto; }
      header .top a.menu::after {
        font-family: 'FontAwesome';
        content: "";
        font-size: 30px;
        line-height: 30px; }
      @media (min-width: 768px) {
        header .top a.menu {
          display: none; } }
  header .bottom {
    position: absolute;
    z-index: 10;
    top: 50px;
    left: 0;
    width: 100%;
    box-shadow: 0px 10px 10px rgba(0, 0, 0, 0.1);
    background: #fff;
    display: none; }
    @media (min-width: 768px) {
      header .bottom {
        position: static;
        box-shadow: none;
        display: block; } }
  header .bottom .contacts {
    background: #f1f1f1;
    z-index: 10;
    position: relative; }
    header .bottom .contacts li {
      padding: 10px 20px; }
      header .bottom .contacts li i {
        font-size: 18px;
        width: 30px; }
    @media (min-width: 768px) {
      header .bottom .contacts {
        display: none; } }
  header nav {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px 0 0 0;
    position: relative; }
    @media (min-width: 768px) {
      header nav {
        padding: 20px 0; } }
    @media (min-width: 768px) {
      header nav > ul {
        display: flex;
        justify-content: space-evenly; } }
    header nav > ul > li {
      position: relative;
      border-bottom: 1px solid #eee; }
      @media (min-width: 768px) {
        header nav > ul > li {
          border-bottom: 0;
          margin: 0 15px; } }
      header nav > ul > li > a {
        font-size: 15px;
        font-weight: 300;
        letter-spacing: 0.5px;
        margin: 0px 15px;
        padding: 10px 0px;
        display: block; }
        @media (min-width: 768px) {
          header nav > ul > li > a {
            margin: 0;
            padding: 0;
            display: inline; } }
        header nav > ul > li > a:hover, header nav > ul > li > a.active {
          color: #063B88;
          text-decoration: none; }
        @media (min-width: 768px) {
          header nav > ul > li > a::after {
            display: inline-block;
            width: 0;
            height: 0;
            margin-left: .255em;
            vertical-align: .255em;
            content: "";
            border-top: .3em solid;
            border-right: .3em solid transparent;
            border-bottom: 0;
            border-left: .3em solid transparent; } }
        @media (min-width: 768px) {
          header nav > ul > li > a:hover, header nav > ul > li > a.active {
            border-bottom: 3px solid #063B88; } }
      @media (min-width: 768px) {
        header nav > ul > li:hover ul {
          display: block !important; } }
    header nav ul ul {
      position: absolute;
      top: 100%;
      left: 0;
      z-index: 1;
      min-width: 10rem;
      padding: .5rem 0;
      margin: .125rem 0 0;
      color: #212529;
      background: #fff;
      border: 1px solid rgba(0, 0, 0, 0.15);
      border-radius: .25rem;
      display: none; }
      header nav ul ul a {
        display: block;
        padding: .25rem 1.5rem;
        color: #212529;
        white-space: nowrap; }
        header nav ul ul a:hover {
          color: #16181b;
          background: #f8f9fa;
          text-decoration: none; }
        header nav ul ul a.active {
          color: #fff;
          background: #063B88; }

footer nav {
  background-color: #232323;
  color: #fff;
  padding: 40px 0px; }
  footer nav a {
    transition: all 0.3s;
    font-size: 14px; }
    footer nav a:hover {
      color: #063B88; }
  footer nav > ul {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    justify-content: space-evenly;
    flex-wrap: wrap; }
    footer nav > ul > li {
      padding: 10px 40px; }
      footer nav > ul > li > a {
        color: #fff;
        font-size: 20px;
        font-weight: 600;
        letter-spacing: 0.25px;
        margin-bottom: 35px;
        display: block;
        margin: 20px 0; }
    footer nav > ul ul li {
      margin: 0 0 6px 0; }
footer .contacts {
  background-color: #343434;
  text-align: center;
  padding: 15px 0px; }
  footer .contacts ul {
    color: #fff;
    margin: 10px 0; }
    @media (min-width: 768px) {
      footer .contacts ul {
        display: flex;
        justify-content: center; }
        footer .contacts ul li {
          margin: 0 20px; } }
    footer .contacts ul li {
      margin: 10px 20px; }
      footer .contacts ul li i {
        width: 34px;
        margin: 0 10px 0 0;
        height: 34px;
        display: inline-block;
        line-height: 34px;
        background-color: #fff;
        color: #232323;
        border-radius: 50%; }
footer .copyright {
  background: #232323;
  color: #fff;
  padding: 20px 0px;
  text-align: center;
  color: #fff;
  font-weight: 300;
  letter-spacing: 0.5px; }

main {
  padding: 10px 0 0 0; }
  main a:hover {
    text-decoration: underline; }
  main .slider {
    background: #000; }
    main .slider .item {
      height: 95vh;
      overflow: hidden;
      position: relative;
      background-size: cover;
      background-position: center center;
      background-repeat: no-repeat;
      margin: 0 5px; }
      main .slider .item:before {
        content: '';
        z-index: 1;
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        background-color: rgba(0, 0, 0, 0.6); }
      main .slider .item .text {
        z-index: 2;
        text-align: left;
        width: 75%;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%); }
      main .slider .item h4 {
        margin-bottom: 30px;
        text-transform: uppercase;
        font-size: 24px;
        font-weight: 700;
        letter-spacing: 1.5px;
        color: #FFF;
        overflow: hidden;
        animation: fadeOutLeft 1s both; }
        @media (min-width: 768px) {
          main .slider .item h4 {
            font-size: 44px;
            letter-spacing: 2.5px; } }
      main .slider .item p {
        max-width: 570px;
        color: #fff;
        font-size: 15px;
        font-weight: 400;
        line-height: 30px;
        margin-bottom: 40px; }
    main .slider .slick-arrow {
      position: absolute;
      top: 50%;
      border: 0 none;
      background-color: transparent;
      text-align: center;
      font-size: 36px;
      font-family: 'FontAwesome';
      color: #FFF;
      z-index: 5;
      outline: none;
      cursor: pointer; }
      main .slider .slick-arrow.slick-prev {
        left: 30px; }
        main .slider .slick-arrow.slick-prev:before {
          content: '\f104'; }
      main .slider .slick-arrow.slick-next {
        right: 30px; }
        main .slider .slick-arrow.slick-next:before {
          content: '\f105'; }
  main h1.main {
    text-transform: capitalize;
    font-size: 36px;
    font-weight: 700;
    letter-spacing: 2px;
    margin: 100px 20px 100px 20px;
    text-align: center; }
  main h2 {
    font-size: 36px;
    font-weight: 600;
    color: #1e1e1e;
    text-align: center;
    margin: 20px 0; }
    main h2 em {
      font-style: normal;
      color: #063B88; }
  main h3 {
    margin-top: 15px;
    text-transform: uppercase;
    font-size: 15px;
    color: #666;
    letter-spacing: 1px;
    text-align: center;
    margin: 15px 0; }
  main .button {
    background-color: #063B88;
    color: #fff;
    font-size: 13px;
    text-transform: uppercase;
    font-weight: 700;
    padding: 12px 30px;
    border-radius: 30px;
    display: inline-block;
    transition: all 0.3s; }
    main .button:hover {
      background-color: #fff;
      color: #063B88;
      text-decoration: none; }
  main .products {
    max-width: 1400px;
    margin: 0 auto;
    margin-top: 30px; }
    @media (min-width: 768px) {
      main .products {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-evenly;
        margin-top: 60px; }
        main .products li {
          width: 33.3%;
          padding: 0 10px; } }
    main .products li {
      margin-bottom: 50px; }
      main .products li a:hover {
        text-decoration: none; }
      main .products li img {
        width: 100%; }
      main .products li .text {
        background-color: #f7f7f7;
        padding: 30px; }
      main .products li h4 {
        font-size: 20px;
        font-weight: 700;
        letter-spacing: 0.25px;
        margin-bottom: 15px; }
      main .products li p {
        margin-bottom: 20px; }
  main .page-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 45px 45px 45px 30px; }
    main .page-content span {
      text-transform: uppercase;
      font-size: 15px;
      color: #666;
      letter-spacing: 1px;
      margin-bottom: 10px;
      display: block; }
    main .page-content h2 {
      font-size: 36px;
      font-weight: 600;
      margin-bottom: 35px; }
    main .page-content em {
      font-style: normal;
      color: #063B88; }
    main .page-content p {
      margin-bottom: 30px; }
  main .paging {
    display: flex;
    justify-content: center;
    margin: 20px 0;
    border-radius: .25rem; }
    main .paging :first-child a {
      border-top-left-radius: .3rem;
      border-bottom-left-radius: .3rem; }
    main .paging :last-child a {
      border-top-right-radius: .3rem;
      border-bottom-right-radius: .3rem; }
    main .paging a {
      padding: .75rem 1.5rem;
      font-size: 1.25rem;
      line-height: 1.5;
      color: #063B88;
      background-color: #fff;
      display: block;
      border: 1px solid #dee2e6;
      margin-left: -1px;
      height: 55px;
      color: #063B88; }
      main .paging a.prev::before {
        font-family: 'FontAwesome';
        content: ""; }
      main .paging a.next::before {
        font-family: 'FontAwesome';
        content: ""; }
      main .paging a:hover {
        background-color: #e9ecef;
        border-color: #dee2e6;
        text-decoration: none; }
      main .paging a:focus {
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25); }
  main .breadcrumbs {
    display: flex;
    flex-wrap: wrap; }
    main .breadcrumbs a {
      font-size: 14px;
      color: #063B88; }
    main .breadcrumbs li::after {
      font-family: 'FontAwesome';
      content: "";
      margin: 0 10px; }
    main .breadcrumbs li:last-child::after {
      display: none; }
  main .category {
    max-width: 1400px;
    margin: 0 auto; }
    main .category .breadcrumbs {
      justify-content: center; }
    main .category h1 {
      text-transform: capitalize;
      font-size: 26px;
      font-weight: 700;
      letter-spacing: 2px;
      text-align: center;
      margin: 20px 0; }
    main .category .filter {
      position: relative; }
      main .category .filter > a {
        background: #009841; }
        main .category .filter > a:hover {
          background: #fff;
          color: #009841; }
      main .category .filter ul {
        position: absolute;
        top: 100%;
        left: 0;
        z-index: 1;
        min-width: 10rem;
        padding: .5rem 0;
        margin: .125rem 0 0;
        color: #212529;
        background: #fff;
        border: 1px solid rgba(0, 0, 0, 0.15);
        border-radius: .25rem;
        display: none; }
        main .category .filter ul a {
          display: block;
          padding: .25rem 1.5rem;
          color: #212529;
          white-space: nowrap; }
          main .category .filter ul a:hover {
            color: #16181b;
            background: #f8f9fa;
            text-decoration: none; }
          main .category .filter ul a.active {
            color: #fff;
            background: #063B88; }
          main .category .filter ul a::before {
            font-family: 'FontAwesome';
            content: "";
            margin-right: 10px; }
    @media (min-width: 768px) {
      main .category {
        margin-top: 30px; }
        main .category h1 {
          font-size: 36px; } }
  main .product .main-info {
    max-width: 1400px;
    margin: 0 auto;
    margin-top: 10px;
    padding: 0 10px; }
    @media (min-width: 768px) {
      main .product .main-info {
        margin-top: 30px; } }
    main .product .main-info .breadcrumbs {
      margin: 0 0 30px 0; }
    main .product .main-info a.back {
      color: #697587; }
      main .product .main-info a.back:before {
        font-family: 'FontAwesome';
        content: "";
        margin-right: 6px; }
    main .product .main-info .desc {
      margin: 15px 0 0 0; }
      @media (min-width: 768px) {
        main .product .main-info .desc {
          display: flex; } }
      main .product .main-info .desc > a {
        max-width: 40%; }
        main .product .main-info .desc > a img {
          width: 100%;
          padding: 10px;
          border: 1px solid #ccc; }
      main .product .main-info .desc .text {
        margin: 0 0 0 15px; }
        main .product .main-info .desc .text h1 {
          font-size: 14px;
          line-height: 20px;
          letter-spacing: .38px;
          color: #333;
          margin-top: 20px; }
          @media (min-width: 768px) {
            main .product .main-info .desc .text h1 {
              margin-top: 0; } }
        main .product .main-info .desc .text h2 {
          font-size: 22px;
          line-height: 30px;
          font-weight: 700;
          color: #333;
          letter-spacing: .27px;
          margin: 10px 0;
          text-align: left; }
          @media (min-width: 768px) {
            main .product .main-info .desc .text h2 {
              text-align: center;
              margin: 20px 0; } }
        main .product .main-info .desc .text a {
          color: #063B88; }
          main .product .main-info .desc .text a:after {
            font-family: 'FontAwesome';
            content: "";
            margin-left: 6px; }
  main .product .files {
    background: #f1f1f1;
    margin: 30px 0; }
    main .product .files ul {
      max-width: 1400px;
      margin: 0 auto;
      display: flex; }
      main .product .files ul li {
        margin: 0 30px; }
      main .product .files ul a {
        padding: 0 0 0 28px;
        position: relative;
        line-height: 40px;
        font-size: 14px; }
        main .product .files ul a:before {
          position: absolute;
          top: 0;
          left: 0;
          bottom: 0;
          margin: auto;
          content: "";
          width: 17px;
          height: 22px;
          background: url(../image/file-icon.png); }
  main .product .details {
    background: #fafafa; }
    main .product .details .links {
      box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.07);
      margin: 0 0 30px 0; }
      main .product .details .links ul {
        max-width: 1400px;
        margin: 0 auto;
        display: flex; }
        main .product .details .links ul a {
          display: block;
          border-bottom: 3px solid transparent;
          font-size: 16px;
          line-height: 25px;
          padding: 0 0 12px;
          margin: 0 20px; }
          main .product .details .links ul a.active {
            border-bottom: 3px solid #063B88;
            color: #063B88; }
          main .product .details .links ul a:hover {
            text-decoration: none; }
    main .product .details table.attr {
      max-width: 1400px;
      margin: 0 auto;
      width: 100%;
      margin-bottom: 25px;
      font-size: 14px;
      font-weight: 400;
      color: #333;
      width: 100%;
      height: auto;
      border-collapse: collapse; }
      main .product .details table.attr caption {
        font-size: 16px;
        line-height: 40px;
        padding: 0 10px;
        background-color: #eaeaea; }
      main .product .details table.attr tr {
        line-height: 44px;
        border-bottom: 1px solid #eaeaea; }
      main .product .details table.attr th {
        width: 35%; }
      main .product .details table.attr th, main .product .details table.attr td {
        padding: 0 10px;
        vertical-align: top; }
    main .product .details .documents {
      max-width: 1400px;
      margin: 0 auto; }
      main .product .details .documents h4 {
        padding-bottom: 20px;
        font-size: 30px;
        line-height: 40px;
        text-align: left;
        text-transform: none;
        color: #063B88;
        margin: 0 10px; }
      main .product .details .documents a {
        display: block;
        margin-left: 10px;
        color: #333;
        padding: 0 0 0 28px;
        position: relative;
        line-height: 40px;
        font-size: 14px;
        border-bottom: 1px solid #e7e6e6; }
        main .product .details .documents a:before {
          position: absolute;
          top: 0;
          left: 0;
          bottom: 0;
          margin: auto;
          content: "";
          width: 17px;
          height: 22px;
          background: url(../image/file-icon.png); }
  main .product .more {
    max-width: 1400px;
    margin: 0 auto;
    margin-top: 20px; }
    @media (min-width: 768px) {
      main .product .more {
        margin-top: 80px; } }
    main .product .more h5 {
      font-weight: 700;
      font-size: 20px;
      line-height: 23px;
      color: #063B88;
      margin: 0 0 0 10px; }
