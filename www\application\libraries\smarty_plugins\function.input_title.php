<?php

require_once("function.input_text_locale.php");

function smarty_function_input_title($params, &$smarty) {
	extract($params);

	$params["prop"] = "title";
	$params["label"] = isset_or($label, "Заглавие");
	$params["maxlength"] = isset_or($maxlength, 128);
	$params["css"] = isset_or($css, "text title");
	$params["validate"] = isset_or($validate, "required");
	$params["err_msg"] = isset_or($err_msg, "Невалидно заглавие");

	return smarty_function_input_text_locale($params, $smarty);
}
