<?php

abstract class bean implements Serializable {
	
	public static function props() {
		return array();
	}
	
	public static function id_keys() {
		return array("id");
	}
	
	public static function auto() {
		return "id";
	}
	
	private $values = array();
	private $files = array();
	private $ext = array();
	private $prev_values = array();
	
	private static $bean_props = array();
	
	private static $NULL_VALUES = array(
			"int" => 0,
			"float" => 0.0,
			"string" => "",
			"date" => null,
			"time" => null,
			"datetime" => null,
			"enum" => null,
			"arr" => null,
	);
	
	public function __construct($props=array()) {
		self::init_props();
		
		$this->values = self::$bean_props[get_class($this)]["default"];
		
		foreach ($props as $key=>$value) {
			$this->__set($key, $value);
		}
		
		$this->save_prev();
	}
	
	public function get($props=array()) {
		if ($props) {
			$res = array();
			foreach ((array)$props as $name) {
				$res[$name] = $this->__get($name);
			}
			return $res;
		}
		return $this->values;
	}
	
	public function get_values($props=array()) {
		$props = ($props ? $props : array_keys($this->values));
		$res = array();
		foreach ((array)$props as $name) {
			$val = $this->__get($name);
			$prop = $this->get_property($name);
			$res[$name] = ($prop->type == "arr" ? json_encode($val) : $val);
		}
		return $res;
	}
	
	public function set($props, array $allow=array()) {
		$deny = self::$bean_props[get_class($this)]["private"];
		if (static::auto()) {
			$deny[] = static::auto();
		}
		
		foreach ($props as $key=>$value) {
			$allowed = (!$allow || in_array($key, $allow)) && !in_array($key, $deny);
			if ($allowed) {
				$this->__set($key, $value);
			}
		}
	}
	
	public function ext($key=null, $def=null) {
		if ($key !== null) {
			return array_key_exists($key, $this->ext) ? $this->ext[$key] : $def;
		}
		return $this->ext;
	}
	
	public function set_ext($key, $value) {
		$this->ext[$key] = $value;
	}
	
	public static function id_keys_string() {
		$keys = static::id_keys();
		return $keys ? join(",", static::id_keys()) : null;
	}
	
	public function get_id() {
		$keys = static::id_keys();
		if (!$keys) {
			throw new exception("Bean '".get_class($this)."' has no ID defined");
		}
		return array_values($this->get($keys));
	}
	
	public function get_id_string() {
		return join(",", $this->get_id());
	}
	
	public function set_auto($val) {
		$this->__set(static::auto(), $val);
	}
	
	public function is_persisted() {
		foreach (static::id_keys() as $key) {
			if (!empty($this->prev_values[$key])) {
				return true;
			}
		}
		return false;
	}
	
	public function has_changes() {
		return $this->values != $this->prev_values;
	}
	
	public function prev($key) {
		return $this->prev_values[$key];
	}
	
	public function __get($key) {
		if ($this->get_property($key)) {
			return $this->values[$key];
		} elseif (array_key_exists($key, $this->ext)) {
			return $this->ext[$key];
		} else {
			throw new exception("Property '{$key}' not found for bean '".get_class($this)."'");
		}
	}
	
	public function __set($key, $value) {
		$method = "setprop_{$key}";
		if (method_exists($this, $method)) {
			$value = call_user_func_array(array($this, $method), array($value));
		}
		
		if ($this->get_property($key)) {
			$this->set_prop($key, $value);
		} else {
			$this->set_ext($key, $value);
		}
	}
	
	public function serialize() {
		return serialize($this->values);
	}
	public function unserialize($values) {
		self::init_props();
		$this->values = unserialize($values);
	}
	
	public function __toString() {
		$res = get_class($this);
		$res .= "(\n\t#VALUES:";
		foreach ($this->values as $key=>$value) {
			$res .= "\n\t\t{$key}={$value}";
		}
		$res .= "\n\t#EXT:";
		foreach ($this->ext() as $key=>$value) {
			$res .= "\n\t\t{$key}={$value}";
		}
		$res.= "\n)\n";
		return $res;
	}
	
	public function get_file($key) {
		return isset($this->files[$key]) ? $this->files[$key] : null;
	}
	
	private function set_prop($key, $value) {
		$prop_info = $this->get_property($key);
		$type = $prop_info->type;
		
		if ($type == "file" && is_array($value)) {
			$this->files[$key] = $value;
		} else if ($type == "arr") {
			$value = (is_string($value) ? @json_decode($value) : $value);
			$this->values[$key] = ($value ? (array)$value : array());
		} else if (is_scalar($value) || is_null($value)) {
			$this->values[$key] = self::parse_scalar($value, $type, $prop_info->nullable, $prop_info->null_values, $prop_info->enums);
		}
	}
	
	private static function init_props() {
		$class = get_called_class();
		
		if (isset(self::$bean_props[$class])) {
			return;
		}
		
		self::$bean_props[$class]["props"] = array();
		self::$bean_props[$class]["default"] = array();
		self::$bean_props[$class]["private"] = array();
		self::$bean_props[$class]["public"] = array();
		
		foreach (static::props() as $key=>$value) {
			$name = (is_int($key) ? $value : $key);
			$opts = (is_int($key) ? null : $value);
			
			if ($name[0] == "-") {
				$name = mb_substr($name, 1);
				self::$bean_props[$class]["private"][] = $name;
			} else {
				self::$bean_props[$class]["public"][] = $name;
			}
			
			$default = (is_scalar($opts) ? $opts : (isset($opts[0]) ? $opts[0] : null));
			$type = (is_array($opts) && (isset($opts[1])) ? $opts[1] : self::infer_type($default));
			$nullable = false;
			if (mb_substr($type, -1) === "?") {
				$nullable = true;
				$type = mb_substr($type, 0, -1);
			}
			$null_values = (isset($opts["null"]) ? $opts["null"] : (isset(self::$NULL_VALUES[$type]) ? self::$NULL_VALUES[$type] : null));
			$null_values = (is_array($null_values) ? $null_values : array($null_values));
			$enums = isset($opts["enums"]) ? (array)$opts["enums"] : array();
			$conf = isset($opts["conf"]) ? (array)$opts["conf"] : array();
			
			if (is_scalar($default) || is_null($default)) {
				$default = self::parse_scalar($default, $type, $nullable, $null_values, $enums);
			}
			
			self::$bean_props[$class]["props"][$name] = (object)array(
					"type" => $type,
					"nullable" => $nullable,
					"default" => $default,
					"null_values" => $null_values,
					"enums" => $enums,
					"conf" => $conf,
			);
			
			self::$bean_props[$class]["default"][$name] = $default;
		}
		
		if (static::id_keys()) {
			foreach (static::id_keys() as $id) {
				if (!isset(self::$bean_props[$class]["props"][$id])) {
					throw new exception("Bean '{$class}' is missing an ID property named '{$id}'.");
				}
			}
		}
	}
	
	private static function infer_type($value) {
		if (is_bool($value)) {
			return "bool";
		}
		if (is_float($value)) {
			return "float";
		}
		if (is_int($value)) {
			return "int";
		}
		return "string";
	}
	
	private static function parse_scalar($value, $type, $nullable, $null_values, $enums) {
		assert(is_scalar($value) || is_null($value));
		
		if ($nullable && $value === null) {
			return null;
		}
		
		if ($value !== null) {
			$value = trim($value);
		}
		
		if ($type == "bool") {
			$value = $value ? true : false;
		} else if ($type == "int") {
			$value = preg_replace("/\s+/", "", $value);
			$value = intval($value);
		} else if ($type == "float") {
			$value = preg_replace("/\s+/", "", $value);
			$value = floatval($value);
		} else if ($type == "date") {
			$value = self::dateval($value);
		} else if ($type == "time") {
			$value = self::timeval($value);
		} else if ($type == "datetime") {
			$value = self::datetimeval($value);
		} else if ($type == "enum") {
			$value = in_array($value, $enums) ? $value : ($enums ? $enums[0] : null);
		}
		
		if ($nullable && in_array($value, $null_values, true)) {
			return null;
		}
		
		return $value;
	}
	
	private function save_prev() {
		$this->prev_values = $this->values;
	}
	
	private function get_property($prop) {
		$props = self::$bean_props[get_class($this)]["props"];
		return (isset($props[$prop]) ? $props[$prop] : null);
	}
	
	public function validator() {
		$class_name = get_class($this);
		$class = "{$class_name}_validator";
		if (class_exists($class)) {
			return new $class($this);
		}
		throw new exception("validator for bean '{$class_name}' not defined");
	}
	
	public function validate($set="def") {
		return $this->validator()->validate($set);
	}
	
	public function adding() {
		$this->save_files();
	}
	public function added() {
		$this->save_prev();
	}
	public function updating() {
		$this->save_files();
	}
	public function updated() {
		$this->save_prev();
	}
	public function deleting() {
		$this->delete_files();
	}
	public function deleted() {
	}
	
	private function save_files() {
		foreach (self::$bean_props[get_class($this)]["props"] as $key=>$prop) {
			if ($prop->type == "file") {
				$current = $this->$key;
				
				$posted = false;
				$delete = false;
				if (isset($this->files[$key])) {
					$file = $this->files[$key];
					$posted = isset($file["tmp_name"]) && is_uploaded_file($file["tmp_name"]) && isset($file["name"]);
					$delete = isset($file["delete"]);
				}
				
				if ($current && ($posted || $delete)) {
					if (!isset($prop->conf["shared"]) || !$prop->conf["shared"]) {
						$this->delete_file($current);
					}
					$this->$key = "";
				}
				if ($posted) {
					$new = $this->save_file($this->files[$key]);
					$this->$key = $new;
				}
			}
		}
	}
	
	public function delete_files() {
		foreach (self::$bean_props[get_class($this)]["props"] as $key=>$prop) {
			if ($prop->type == "file") {
				$current = $this->$key;
				if ($current) {
					$this->delete_file($current);
				}
			}
		}
	}
	
	public function save_file($file) {
		
	}
	public function delete_file($filename) {
		
	}
	
	private static function datetime_format($input, $format) {
		if ($input === "") {
			return null;
		}
		$timestamp = date_create($input);
		if ($timestamp !== false) {
			return $timestamp->format($format);
		}
		return null;
	}
	private static function dateval($input) {
		return self::datetime_format($input, "Y-m-d");
	}
	private static function timeval($input) {
		return self::datetime_format($input, "H:i:s");
	}
	private static function datetimeval($input) {
		return self::datetime_format($input, "Y-m-d H:i:s");
	}
}
