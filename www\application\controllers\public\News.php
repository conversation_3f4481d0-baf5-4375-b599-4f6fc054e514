<?php

require_once "base/Public_base.php";

class News extends Public_base {

	public function category($slug) {
		$node = $this->get_category($slug);

		if ($node) {
			$filter = array(
					"parent_id" => $node->id, 
			);

			$list = $this->data->nodes->get_list_loc($this->lng->id, $filter, "position", "desc");

			$this->view->set("list", $list);
		}

		$this->render_node("public/news-list", $node);
	}

	public function details($slug1, $slug2) {
		$node = $this->get_category($slug1);
		
		if ($node) {
			$filter = array(
					"parent_id" => $node->id,
					"slug" => urldecode($slug2),
			);
			$node = $this->data->nodes->get_first_loc($this->lng->id, $filter);

			if ($node) {
				$filter = array(
					"parent_id" => $node->parent_id,
					"not_id" => $node->id,
				);
				$list = $this->data->nodes->get_list_loc($this->lng->id, $filter, "position", "desc", 0, 2);
				$this->view->set("list", $list);
			}
		}

		$this->render_node("public/news-details", $node);
	}

	private function get_category($slug) {
		$filter = array(
			"parent_id" => nodeid("news"),
			"slug" => urldecode($slug),
		);
		return $this->data->nodes->get_first_loc($this->lng->id, $filter);
	}
}
