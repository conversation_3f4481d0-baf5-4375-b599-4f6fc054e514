<?php

require_once("function.input_text.php");

function smarty_function_input_text_locale($params, &$smarty) {
	extract($params);

	$object = arr($params, "object", $smarty->getVariable("object")->value);
	$lang = $smarty->getVariable("lang")->value;

	$name_prefix = arr($params, "prefix", "object");
	$params["name"] = sprintf("%s[locale_data][%s][%s]", $name_prefix, $lang->id, $prop);

	if (!array_key_exists("value", $params)) {
		$params["value"] = $object->locale($lang->id)->$prop;
	}

	$params["err"] = isset_or($value, array(array($lang->id, $prop)));

	if (isset($err_msg)) {
		$params["err_msg"] = $err_msg . " - " . $lang->title;
	}

	return smarty_function_input_text($params, $smarty);
}
