<?php

function send_email($subject, $message, $recipient, $sender="", $format="", $attachments=array()) {
	$CI =& get_instance();
	$CI->config->load("email");
	$CI->load->library("email");
	$CI->email->clear(true);

	$config = config_item("email");
	$config["mailtype"] = ($format == "text" ? $format : "html");

	$sender = ($sender ? $sender : $config["default_sender"]);

	$CI->email->initialize($config);

	$CI->email->to($recipient);
	$CI->email->from($sender);
	$CI->email->subject($subject);
	$CI->email->message($message);

	foreach ($attachments as $a) {
		$CI->email->attach($a);
	}

	$CI->email->send();
}

