<?php

class Emailer extends CI_Controller {
	public function __construct() {
		parent::__construct();

		if (!in_array($this->input->ip_address(), conf("service_allow_ip"))) {
			die("not allowed");
		}
		$this->classloader->load("common");

		$this->load->library("datamanager", null, "data");
		$this->load->library("ci_smarty", null, "view");
	}

	public function index() {

		if ($this->input->is_cli_request()) {
			$this->send_all();
		}
	}
	private function send_all() {
		// deprecated
		return;
		$messages = $this->data->email_messages->get_pending();

		$sent = 0;
		foreach($messages as $message) {
			$format = ($message->is_plain ? "text" : "html");
			$body = $this->view->result($message->template, true, $message->model);
			send_email($message->subject, $body, $message->to, $message->from, $format);
			$message->is_sent = true;
			$this->data->update($message);
			$sent++;
		}

		$log = "sent: {$sent}";

		//if ($sent) {
			$this->log->log_cronjob("emailer", $log);
		//}
		$this->output->append_output($log);
	}
}