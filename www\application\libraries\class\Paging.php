<?php

class Paging {
	private $items;
	private $size;
	private $index;
	
	private $pages;
	private $offset;
	
	private $delta;
	private $left;
	private $right;

	public function __construct($items, $size, $index, $delta=0) {
		$this->items = $items;
		$this->size = ($size > 0 ? $size : PHP_INT_MAX);
		$this->pages = ceil($this->items / $this->size);
		$this->index = max(min($index, $this->pages), 1);
		$this->delta = $delta;
		$this->offset = ($this->index - 1) * $this->size;

		$this->left = 1;
		$this->right = $this->pages;
		
		if ($delta > 0) {
			if ($this->index <= $this->delta) {
				$this->left = 1;
				$this->right = min($this->pages, 1 + 2 * $this->delta);
			} elseif ($this->index > $this->pages - $this->delta) {
				$this->left = max(1, $this->pages - 2 * $this->delta);
				$this->right = $this->pages;
			} else {
				$this->left = max(1, $this->index - $this->delta);
				$this->right = min($this->pages, $this->index + $this->delta);
			}
		}
	}
	
	public function items() { return $this->items; }
	public function size() { return $this->size; }
	public function index() { return $this->index; }
	public function pages() { return $this->pages; }
	public function offset() { return $this->offset; }

	public function prev() {
		return max(1, $this->index - 1);
	}
	public function next() {
		return min($this->pages, $this->index + 1);
	}
	
	public function left_end() {
		return min($this->left - 1, $this->delta);
	}
	public function left_break() {
		return $this->left > $this->delta + 1;
	}
	public function mid_start() {
		return $this->left;
	}
	public function mid_end() {
		return $this->right;
	}
	public function right_break() {
		return $this->right < $this->pages - $this->delta;
	}
	public function right_start() {
		return max($this->right + 1, $this->pages - $this->delta + 1);
	}
}