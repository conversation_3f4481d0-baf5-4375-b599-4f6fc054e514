<?php

class AssetType {

	const IMAGE = 1;
	const VIDEO = 2;
	const FILE = 4;
	const EMBED = 8;
	const ATTR = 16;
	const DOCUMENT = 32;
	const FEATURE = 64;

	const EMBED_YOUTUBE = 1;
	const EMBED_VIMEO = 2;

	public static $TYPES = array (
			self::IMAGE => array("description"=>"изображения", "file"=>true, "url"=>false, "ext"=>"gif|jpe?g|png"),
			self::VIDEO => array("description"=>"видео", "file"=>true, "url"=>false, "ext"=>"mp4|ogg|webm"),
			self::FILE => array("description"=>"файлове", "file"=>true, "url"=>false, "ext"=>".*"),
			self::EMBED => array("description"=>"видео", "file"=>false, "url"=>true, "ext"=>null),
	);

	public static function match($type) {
		$match = array();
		foreach (self::$TYPES as $k=>$t) {
			if ($type & $k) {
				$match[$k] = $t;
			}
		}
		return $match;
	}

	private static function resolve_one($type) {
		$match = self::match($type);
		if ($match) {
			$desc = array();
			$file = false;
			$url = false;
			$ext = array();
			foreach ($match as $m) {
				$desc[] = $m["description"];
				$file = $file || $m["file"];
				$url = $url || $m["url"];
				if ($m["ext"]) {
					$ext[] = $m["ext"];
				}
			}
			return array("description"=>implode(" & ", $desc), "file"=>$file, "url"=>$url, "ext"=>implode("|", $ext));

		}
		return null;
	}
	
	public static function resolve() {
		$res = array();
		foreach (func_get_args() as $arg) {
			$type = self::resolve_one($arg);
			if ($type) {
				$res[$arg] = $type;
			}
		}
		return $res;
	}
	
	public static function resolve_ext($type, $ext) {
		foreach (AssetType::match($type) as $key=>$type) {
			if ($type["ext"]) {
				$pattern = "/" . $type["ext"] . "/";
				if (preg_match($pattern, $ext)) {
					return $key;
				}
			}
		}
		
		return null;
	}
}