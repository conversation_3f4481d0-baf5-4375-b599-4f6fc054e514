<?php

require_once("variablefilter.htmlstring.php");

function smarty_function_options($params, &$smarty) {
	$opts = (array)arr($params, "opts", array());
	$val = arr($params, "val", "");
	$text = arr($params, "text", "");
	$sel = (array)arr($params, "sel", null);
	$def = arr($params, "def");

	$text_format = null;
	$text_params = null;

	if (is_array($text)) {
		$text_format = array_shift($text);
		$text_params = $text;
	}

	$res = "";

	if ($def) {
		$res .= "<option value=\"\">{$def}</option>";
	}

	foreach ($opts as $index=>$el) {
		if (is_scalar($el) || is_null($el)) {
			$opt_val = ($val == "#" ? $index : $el);
			$opt_text = $el;
		} else {
			$opt_val = ($val == "#" ? $index : smarty_function_options_get_val($el, $val));

			if ($text_format === null) {
				$opt_text = smarty_function_options_get_val($el, $text);
			} else {
				$printf_params = array();
				foreach ($text_params as $p) {
					$printf_params[] = ($p == "#" ? $index : smarty_function_options_get_val($el, $p));
				}
				$opt_text = vsprintf($text_format, $printf_params);
			}
		}

		$selected = in_array($opt_val, $sel) ? $selected = ' selected="selected"' : "";
		$opt_val = smarty_variablefilter_htmlstring($opt_val, $smarty);
		$opt_text = smarty_variablefilter_htmlstring($opt_text, $smarty);

		$res .= "<option value=\"{$opt_val}\"{$selected}>{$opt_text}</option>";
	}
	return $res;
}

function smarty_function_options_get_val($el, $prop) {
	if ($prop === null || $prop === "") {
		$res = print_r($el, true);
	} else if (is_array($el)) {
		return $el[$prop];
	} else if (is_object($el)) {
		eval('$res = $el->'.$prop. ";");
		return $res;
	}
}

