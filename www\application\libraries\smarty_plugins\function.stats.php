<?php

function smarty_function_stats($params, &$smarty) {
	extract($params);
	$CI =& get_instance();
	$time = $CI->benchmark->elapsed_time("total_execution_time_start");
	$mem = round(memory_get_usage()/(1024*1024), 2)."MB";
	$queries = "";
	if (isset($verbose) && $verbose) {
		foreach ($CI->data->queries() as $q) {
			$queries .= $q[0] . "\n";
		}
	}
	$count = count($CI->data->queries());
	return sprintf('%.3fs | %s | <span title="%s">%dq</span>', $time, $mem, $queries, $count);
}
