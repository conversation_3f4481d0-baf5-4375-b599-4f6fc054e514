<?php
/*
 * numeric positions are like so:
	* * * * * * *
	* 1   2   3 *
	*           *
	* 4   5   6 *
	*           *
	* 7   8   9 *
	* * * * * * *

	e.g. 4 = position2d::MIDDLE_LEFT()
*/
class Position2D {
	const LOW = 0;
	const MID = 1;
	const HIGH = 2;

	private $point1d_x;
	private $point1d_y;

	private function __construct($point1d_x, $point1d_y) {
		$this->point1d_x = $point1d_x;
		$this->point1d_y = $point1d_y;
	}
	public static function from_index($val) {
		$x = ($val - 1) % 3;
		$y = (int)(($val - 1) / 3);
		return new self($x, $y);
	}

	public function get_point_x() {
		return $this->point1d_x;
	}
	public function get_point_y() {
		return $this->point1d_y;
	}
	public static function TOP_LEFT() {
		return new self(self::LOW, self::LOW);
	}
	public static function TOP_MIDDLE() {
		return new self(self::MID, self::LOW);
	}
	public static function TOP_RIGHT() {
		return new self(self::HIGH, self::LOW);
	}
	public static function MIDDLE_LEFT() {
		return new self(self::LEFT, self::MID);
	}
	public static function CENTER() {
		return new self(self::MID, self::MID);
	}
	public static function MIDDLE_RIGHT() {
		return new self(self::HIGH, self::MID);
	}
	public static function BOTTOM_LEFT() {
		return new self(self::LOW, self::HIGH);
	}
	public static function BOTTOM_MIDDLE() {
		return new self(self::MID, self::HIGH);
	}
	public static function BOTTOM_RIGHT() {
		return new self(self::HIGH, self::HIGH);
	}
	public function get_inner_point($container, $inner) {
		$x = $this->get_x($container[0]) - $this->get_x($inner[0]);
		$y = $this->get_y($container[1]) - $this->get_y($inner[1]);

		return array($x, $y);
	}
	public function get_x($point) {
		switch($this->point1d_x) {
			case self::LOW:
				return 0;
				break;
			case self::MID:
				return $point/2;
				break;
			case self::HIGH:
				return $point;
				break;
		}
	}
	public function get_y($point) {
		switch($this->point1d_y) {
			case self::LOW:
				return 0;
				break;
			case self::MID:
				return $point/2;
				break;
			case self::HIGH:
				return $point;
				break;
		}
	}
}