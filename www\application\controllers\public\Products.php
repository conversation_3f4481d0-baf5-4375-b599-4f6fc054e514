<?php

require_once "base/Public_base.php";

class Products extends Public_base {

	public function index() {
		$slugs = func_get_args();
		if ($slugs) {
			$slugs = array_map("urldecode", $slugs);
			array_unshift($slugs, $this->get_node("products")->url);
			
			$full_slug =  implode("/", $slugs);
			$node = $this->data->nodes->get_first_loc($this->lng->id, array(
				"full_url" => $full_slug,
			));
			if ($node && $node->kind == node::KIND_CAT) {
				$this->category($node);
			} elseif ($node && $node->kind == node::KIND_PROD) {
				$this->product($node);
			} else {
				$this->_404();
			}
		} else {
			$this->category($this->node());
		}
	}

	private function category($node) {
		$breadcrumbs = $this->data->nodes->get_list_loc($this->lng->id, array(
			"end" => $node->id,
			"min_length" => 1,
			"max_length" => 10,
		));
		array_shift($breadcrumbs);
		$this->view->set("breadcrumbs", $breadcrumbs);

		$subcats = $this->data->nodes->get_list_loc($this->lng->id, array(
			"parent_id" => $node->id,
			"kinds" => node::KIND_CAT,
		));
		$this->view->set("subcats", $subcats);

		$page = _get("page");
		$term = _get("term");
		$filter = array(
				"start" => $node->id, 
				"kinds" => node::KIND_PROD,
				"min_length" => 0,
				"max_length" => 10,
				"term" => $term,
		);
		$total = $this->data->nodes->get_count_loc($this->lng->id, $filter);
		$paging = new Paging($total, 12, $page, 2);

		$list = $this->data->nodes->get_list_loc($this->lng->id, $filter, "position", "asc", $paging->offset(), $paging->size());

		$this->set_paging_meta($paging);

		$this->view->set("term", $term);
		$this->view->set("paging", $paging);
		$this->view->set("list", $list);

		$this->render_node("public/category", $node);
	}

	private function product($node) {
		$breadcrumbs = $this->data->nodes->get_list_loc($this->lng->id, array(
			"end" => $node->id,
			"min_length" => 1,
			"max_length" => 10,
		));
		array_shift($breadcrumbs);
		$this->view->set("breadcrumbs", $breadcrumbs);

		$page = _get("page");
		$filter = array(
				"parent_id" => $node->parent_id, 
				"not_id" => $node->id,
				"kinds" => node::KIND_PROD,
		);
		$list = $this->data->nodes->get_list_loc($this->lng->id, $filter, "position", "asc", 0, 6);
		$this->view->set("list", $list);

		$documents = $this->data->assets->get_list_loc($this->lng->id, array(
			"node_id" => $node->id,
			"type" => AssetType::DOCUMENT,
		));
		$this->view->set("documents", $documents);

		$data = $this->data->assets->get_list_loc($this->lng->id, array(
			"node_id" => $node->id,
			"type" => AssetType::ATTR,
		));
		$attr = __::groupBy($data, function($o) {
			return $o->feat1;
		});
		$this->view->set("attr", $attr);

		if ($node->has_form) {
			$this->order($node);
		}

		$this->render_node("public/product", $node);
	}

	public function suggest() {
		if ($this->input->is_ajax_request()) {
			$term = _post("term");
	
			$data = $this->data->nodes->get_list_loc($this->lng->id, array(
				"kinds"	=> node::KIND_PROD,
				"term" => $term,
			), "position", "asc", 0, 20);
	
			$res = __::map($data, function ($o) {
				return array (
						"title" => $o->title,
						"desc" => $o->subtitle,
						"image" => ($o->image ? file_image_url($o->image, "search") : ""),
						"url" => $this->path->base . $o->full_url,
				);
			});
			$this->json_response($res);
		}
	}

	private function order($node) {
		$validator = new Validator();
		$model = array("name"=>"", "phone"=>"", "email"=>"", "message"=>"");
		
		if (_post()) {
			$filtered = array_intersect_key(_post_arr("model"), $model);
			$model = array_merge($model, $filtered);

			$validator->required($model["name"], "name");
			$validator->email($model["email"], "email");
			$validator->jstest(_post("jstest"), "jstest");

			if ($validator->is_valid()) {
				$model["product"] = $node->title;
				
				$subject = "Форма за поръчки";
				$recipients = $this->_var("product-form");
				$this->send_email("order", $model, $subject, $recipients);

				$this->set_flash("status");
				refresh();
			}
		}
		
		$form = array(
				"jstest" => jstest(), 
				"model" => $model,
				"messages" => array(
					"name" => "Невалидно име",
					"email" => "Невалиден имейл",
					"jstest" => "Изглежда, че сте робот",
					"success" => "Вашета поръчка беше получена",
				),
				"errors" => $validator->get_invalid(),
				"success" => $this->get_flash("status"),
		);
		$this->view->set("form", $form);
	}
}
